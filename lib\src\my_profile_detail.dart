
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/src/pages/cross_platform/wide_screen/about_us.dart';
import 'package:tencent_cloud_chat_demo/src/pages/cross_platform/wide_screen/contact_us.dart';
import 'package:tencent_cloud_chat_demo/src/pages/cross_platform/wide_screen/settings.dart';
import 'package:tencent_cloud_chat_demo/src/pages/qrcode/qrcode.dart';
import 'package:tencent_cloud_chat_demo/src/routes.dart';
import 'package:tencent_cloud_chat_demo/utils/constant.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import 'package:tencent_cloud_chat_demo/utils/user_info_local.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';
import 'package:tencent_cloud_chat_uikit/data_services/core/tim_uikit_wide_modal_operation_key.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/time_ago.dart';

import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitProfile/widget/tim_uikit_profile_widget.dart';

import 'package:tencent_cloud_chat_demo/config.dart';
import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/wide_popup.dart';
import '../../../../../apis/tuils_api.dart';
import '../../../../../models/upload_response.dart';
import 'package:image_picker/image_picker.dart';
import '../../widgets/avatar.dart';
import './widgets/input_dialog.dart';
import './widgets/bottom_sheet_picker.dart';
import 'package:flutter/services.dart';

class MyProfileDetail extends StatefulWidget {
  final V2TimUserFullInfo? userProfile;
  final TIMUIKitProfileController? controller;

  const MyProfileDetail({Key? key, this.userProfile, this.controller})
      : super(key: key); 

  @override
  State<StatefulWidget> createState() => MyProfileDetailState();
}

class MyProfileDetailState extends State<MyProfileDetail> {
  final CoreServicesImpl _coreServices = TIMUIKitCore.getInstance();
  late V2TimUserFullInfo? userProfile;
  late DateTime selectedDate;
  String phone = '';
  final ImagePicker _picker = ImagePicker();
  List<XFile> _imageFiles = [];
  @override
  void initState() {
    super.initState();
    userProfile = widget.userProfile;
    if (userProfile?.birthday != null && userProfile?.birthday != 0) {
      final date = DateTime.parse(userProfile!.birthday.toString());
      selectedDate = date;
    } else {
      selectedDate = DateTime.now();
    }

    _loadUserInfo();
  }

  _handleLogout(BuildContext context) async {
    debugPrint("click logout");
    final res = await _coreServices.logout();
    if (res.code == 0) {
      debugPrint("logout success");
      try {
        Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
        SharedPreferences prefs = await _prefs;
        prefs.remove(Const.DEV_LOGIN_USER_ID);
        prefs.remove(Const.DEV_LOGIN_USER_SIG);
        prefs.remove(Const.SMS_LOGIN_TOKEN);
        prefs.remove(Const.SMS_LOGIN_PHONE);
      } catch (err) {
        ToastUtils.log("someError");
        ToastUtils.log(err);
      }
      Routes().directToLoginPage();
    }
  }

  showGenderChoseSheet(BuildContext context, TUITheme? theme) {
    // 使用组件
    BottomSheetPicker.show(
      context: context,
      title: TIM_t("性别"),
      options: [
        BottomSheetOption(
          title: TIM_t("男"),
          isSelected: userProfile?.gender == 1,
          onTap: () async {
            final res = await widget.controller?.updateGender(1);
            if (res?.code == 0) {
              setState(() {
                userProfile?.gender = 1;
              });
            }
          },
        ),
        BottomSheetOption(
          title: TIM_t("女"),
          isSelected: userProfile?.gender == 2,
          onTap: () async {
            final res = await widget.controller?.updateGender(2);
            if (res?.code == 0) {
              setState(() {
                userProfile?.gender = 2;
              });
            }
          },
        ),
      ],
      cancelText: TIM_t("取消"),
    );
  }


  String handleGender(int gender) {
    switch (gender) {
      case 0:
        return TIM_t("未设置");
      case 1:
        return TIM_t("男");
      case 2:
        return TIM_t("女");
      default:
        return "";
    }
  }

  Future<void> showSelectAvatarPage(BuildContext context) async {
    BottomSheetPicker.show(
      context: context,
      title: "",
      options: [
        BottomSheetOption(
          title: TIM_t("拍摄"),
          onTap: () {
            _takePhoto();
          },
        ),
        BottomSheetOption(
          title: TIM_t("从手机相册选择"),
          onTap: () {
            _pickImage();
          },
        )
      ],
      cancelText: TIM_t("取消"),
    );
  }
  // 拍照
  Future<void> _takePhoto() async {
    try {
      final XFile? photo = await _picker.pickImage(source: ImageSource.camera, imageQuality: 80);
      if (photo != null) {
        debugPrint("选择拍照图片成功: ${photo.toString()}");

                // 裁剪图片为正方形
        final croppedFile = await ImageCropper().cropImage(
          sourcePath: photo.path,
          aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
          compressQuality: 80,
          uiSettings: [
            AndroidUiSettings(
              toolbarTitle: TIM_t('裁剪头像'),
              statusBarColor: const Color(0xFF000000), // 使用明确的黑色值
              toolbarColor: Colors.white,
              toolbarWidgetColor: Colors.black,
              lockAspectRatio: true,
              cropFrameColor: Colors.blue,
              cropGridColor: Colors.blue,
              activeControlsWidgetColor: Colors.blue,
              dimmedLayerColor: Colors.black.withOpacity(0.5),
              initAspectRatio: CropAspectRatioPreset.square,
              hideBottomControls: true,
              showCropGrid: true,
            ),
            IOSUiSettings(
              title: TIM_t('裁剪头像'),
              aspectRatioLockEnabled: true,
              resetAspectRatioEnabled: false,
              doneButtonTitle: TIM_t('完成'),
              cancelButtonTitle: TIM_t('取消'),
              rotateButtonsHidden: true,
              resetButtonHidden: true,
              aspectRatioPickerButtonHidden: true,
              rotateClockwiseButtonHidden: true,
              hidesNavigationBar: false,
            ),
          ],
        );
        if (croppedFile != null) {
          final croppedXFile = XFile(croppedFile.path);
          setState(() {
            _imageFiles.add(croppedXFile);
          });
          // 上传图片
          List<Map<String, Object>> imageUrls = await _uploadImages();
          debugPrint("上传拍照图片结果: ${imageUrls.toString()}");
          final avatarUrl = imageUrls[0]['mediaUrl'] as String;
          widget.controller?.updateAvatar(avatarUrl);
          setState(() {
            userProfile?.faceUrl = avatarUrl;
            _imageFiles.clear();
          });
        }

        
      }
    } catch (e) {
      print("拍照出错: $e");
    }
  }

  // 从相册选择
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.gallery, imageQuality: 80);
      if (image != null) {
        debugPrint("选择图片成功: ${image.toString()}");

        // 裁剪图片为正方形
        final croppedFile = await ImageCropper().cropImage(
          sourcePath: image.path,
          aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
          compressQuality: 80,
          uiSettings: [
            AndroidUiSettings(
              toolbarTitle: TIM_t('裁剪头像'),
              statusBarColor: const Color(0xFF000000), // 使用明确的黑色值
              toolbarColor: Colors.white,
              toolbarWidgetColor: Colors.black,
              lockAspectRatio: true,
              cropFrameColor: Colors.blue,
              cropGridColor: Colors.blue,
              activeControlsWidgetColor: Colors.blue,
              dimmedLayerColor: Colors.black.withOpacity(0.5),
              initAspectRatio: CropAspectRatioPreset.square,
              hideBottomControls: true,
              showCropGrid: true,
            ),
            IOSUiSettings(
              title: TIM_t('裁剪头像'),
              aspectRatioLockEnabled: true,
              resetAspectRatioEnabled: false,
              doneButtonTitle: TIM_t('完成'),
              cancelButtonTitle: TIM_t('取消'),
              rotateButtonsHidden: true,
              resetButtonHidden: true,
              aspectRatioPickerButtonHidden: true,
              rotateClockwiseButtonHidden: true,
              hidesNavigationBar: false,
            ),
          ],
        );
        
        if (croppedFile != null) {
          final croppedXFile = XFile(croppedFile.path);
          setState(() {
            _imageFiles.add(croppedXFile);
          });
          // 上传图片
          List<Map<String, Object>> imageUrls = await _uploadImages();
          final avatarUrl = imageUrls[0]['mediaUrl'] as String;
          debugPrint("上传图片成功: ${avatarUrl}");
          widget.controller?.updateAvatar(avatarUrl);
          setState(() {
            userProfile?.faceUrl = avatarUrl;
            _imageFiles.clear();
          });
        }
      }
    } catch (e) {
      ToastUtils.toast("选择图片出错");
    }
  }

  // 上传图片
  Future<List<Map<String, Object>>> _uploadImages() async {
    List<Map<String, Object>> uploadedUrls = [];

    try {
      for (XFile image in _imageFiles) {
        // 创建FormData对象
        FormData formData = FormData.fromMap({
          "file": await MultipartFile.fromFile(
            image.path,
            filename: image.name,
          ),
          "folder": 1
        });
        debugPrint("上传图片结果: ${formData.toString()}");
        // 调用上传接口
        var result = await Api.instance.upload(formData);
        debugPrint("上传图片结果: ${result.toString()}");
        if (result is UploadResponse &&
            result.code == 0 &&
            result.data != null) {
          String? url = result.data?.fileUrl; // 修改这里，使用fileUrl而不是url
          if (url != null && url.isNotEmpty) {
            var obj = {
              "mediaType": "image",
              "mediaUrl": url,
              "sortOrder": 0,
            };
            uploadedUrls.add(obj);
          }
        }
      }
      return uploadedUrls;
    } catch (e) {
      debugPrint("上传图片失败: $e");
      ToastUtils.toast("上传图片失败");
      return [];
    }
  }

  // 异步加载用户信息
  Future<void> _loadUserInfo() async {
    try {
      final localUserInfo = await UserInfoLocal.getUserInfo();
      if (localUserInfo != null) {
        setState(() {
          phone = localUserInfo.phone ?? '';
        });
      }
    } catch (e) {
      debugPrint('加载用户信息出错: $e');
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
    );
    if (pickedDate != null && pickedDate != selectedDate) {
      String birthdayString = pickedDate.year.toString() +
          TimeAgo.getMonth(pickedDate) +
          TimeAgo.getDay(pickedDate);
      final result =
          await widget.controller?.updateBirthday(int.parse(birthdayString));
      if (result?.code == 0) {
        setState(() {
          selectedDate = pickedDate;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Provider.of<DefaultThemeData>(context).theme;
    final isWideScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: isWideScreen
          ? null
          : AppBar(
              shadowColor: theme.weakDividerColor,
              elevation: 1,
              title: Text(
                TIM_t("个人资料"),
                style:
                    const TextStyle(fontSize: IMDemoConfig.appBarTitleFontSize),
              ),
              backgroundColor: const Color(0xFFFFFFFF),
              surfaceTintColor: Colors.white,
            ),
      body: Container(
        color: const Color(0xFFF6F6F6),
        child:Column(
          children: [
            if (isWideScreen)
              TIMUIKitProfileUserInfoCard(
                  onClickAvatar: () => showSelectAvatarPage(context),
                  userInfo: userProfile),
            if (!isWideScreen)
              GestureDetector(
                child: Container(
                  color: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        TIM_t("头像"),
                        style: const TextStyle(
                          fontSize: 16,
                          color: Color(0xFF333333),
                        ),
                      ),
                      Row(
                        children: [
                          Avatar(
                            avatarUrl: userProfile?.faceUrl,
                            size: 46,
                            radius: 23,
                          ),
                          const Icon(Icons.keyboard_arrow_right)
                        ],
                      )
                    ],
                  ),
                ),
                onTap: () => showSelectAvatarPage(context),
              ),
            const SizedBox(
              height: 16,
            ),
            InkWell(
              onTapDown: (details) async {
                final String? newNickname = await InputDialog.show(
                  context: context,
                  title: "修改昵称",
                  hintText: "请输入昵称",
                  initialValue: userProfile?.nickName ?? '',
                );
                if (newNickname != null) {
                  final res =
                      await widget.controller?.updateNickName(newNickname);
                  if (res?.code == 0) {
                    setState(() {
                      userProfile?.nickName = newNickname;
                    });
                  }
                }
              },
              child: TIMUIKitOperationItem(
                isEmpty: !TencentUtils.isTextNotEmpty(userProfile?.nickName),
                operationName: TIM_t("昵称"),
                operationRightWidget: Text(
                    TencentUtils.isTextNotEmpty(userProfile?.nickName)
                        ? userProfile!.nickName!
                        : isWideScreen
                            ? ""
                            : TIM_t("未填写"),
                    textAlign: isWideScreen ? null : TextAlign.end),
              ),
            ),
            TIMUIKitProfileWidget.userAccountBar(
              userProfile?.userID ?? "",
              false,
            ),
            InkWell(
              onTapDown: (details) async {
                // final String? newNickname = await InputDialog.show(
                //   context: context,
                //   title: "修改手机号",
                //   hintText: "请输入手机号",
                //   initialValue: phone,
                // );
                // if (newNickname != null) {
                //   final res =
                //       await widget.controller?.updateNickName(newNickname);
                //   if (res?.code == 0) {
                //     setState(() {
                //       userProfile?.nickName = newNickname;
                //     });
                //   }
                // }
              },
              child: TIMUIKitOperationItem(
                isEmpty: !TencentUtils.isTextNotEmpty(userProfile?.nickName),
                operationName: TIM_t("手机号"),
                operationRightWidget: Text(
                    TencentUtils.isTextNotEmpty(phone)? phone: isWideScreen? "" : TIM_t("未填写"),
                    textAlign: isWideScreen ? null : TextAlign.end),
              ),
            ),
            InkWell(
              onTapDown: (details) async {
                if (userProfile != null) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => Qrcode(userInfo: userProfile!),
                    ),
                  );
                }
              },
              child: TIMUIKitOperationItem(
                isEmpty: !TencentUtils.isTextNotEmpty(userProfile?.nickName),
                operationName: TIM_t("二维码"),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            InkWell(
                onTapDown: (details) async {
                  // widget.controller?.showTextInputBottomSheet(
                  //     context: context,
                  //     title: TIM_t("修改签名"),
                  //     tips: TIM_t("仅限汉字、英文、数字和下划线"),
                  //     initOffset: Offset(
                  //         min(details.globalPosition.dx,
                  //             MediaQuery.of(context).size.width - 400),
                  //         min(details.globalPosition.dy,
                  //             MediaQuery.of(context).size.height - 100)),
                  //     onSubmitted: (String selfSignature) async {
                  //       final res = await widget.controller
                  //           ?.updateSelfSignature(selfSignature);
                  //       if (res?.code == 0) {
                  //         setState(() {
                  //           userProfile?.selfSignature = selfSignature;
                  //         });
                  //       }
                  //     },
                  //     theme: theme);
                  final String? newSignature = await InputDialog.show(
                    context: context,
                    title: "修改签名",
                    hintText: "请输入签名",
                    initialValue: userProfile?.selfSignature ?? '',
                  );
                  if (newSignature != null) {
                    final res = await widget.controller
                        ?.updateSelfSignature(newSignature);
                    if (res?.code == 0) {
                      setState(() {
                        userProfile?.selfSignature = newSignature;
                      });
                    }
                  }
                },
                child: TIMUIKitOperationItem(
                    isEmpty: !TencentUtils.isTextNotEmpty(
                        userProfile?.selfSignature),
                    operationName: TIM_t("个性签名"),
                    operationRightWidget: Text(
                        TencentUtils.isTextNotEmpty(userProfile?.selfSignature)
                            ? userProfile!.selfSignature!
                            : isWideScreen
                                ? ""
                                : TIM_t("未填写"),
                        textAlign: isWideScreen ? null : TextAlign.end))),
            InkWell(
                onTapDown: (details) {
                  if (isWideScreen) {
                    TUIKitWidePopup.showPopupWindow(
                        isDarkBackground: false,
                        operationKey: TUIKitWideModalOperationKey
                            .secondaryClickUserAvatar,
                        borderRadius:
                            const BorderRadius.all(Radius.circular(4)),
                        context: context,
                        offset: Offset(details.globalPosition.dx,
                            details.globalPosition.dy),
                        child: (closeFunc) => TUIKitColumnMenu(
                              data: [
                                ColumnMenuItem(
                                    label: TIM_t("男"),
                                    onClick: () async {
                                      final res = await widget.controller
                                          ?.updateGender(1);
                                      if (res?.code == 0) {
                                        setState(() {
                                          userProfile?.gender = 1;
                                        });
                                      }
                                      closeFunc();
                                    }),
                                ColumnMenuItem(
                                    label: TIM_t("女"),
                                    onClick: () async {
                                      final res = await widget.controller
                                          ?.updateGender(2);
                                      if (res?.code == 0) {
                                        setState(() {
                                          userProfile?.gender = 2;
                                        });
                                      }
                                      closeFunc();
                                    }),
                              ],
                            ));
                  } else {
                    showGenderChoseSheet(context, theme);
                  }
                },
                child: TIMUIKitProfileWidget.genderBarWithArrow(
                    userProfile?.gender ?? 0, false)),
            InkWell(
                onTapDown: (details) {
                  _selectDate(context);
                },
                child: TIMUIKitProfileWidget.birthdayBar(
                    userProfile?.birthday ?? 0, false)),
            if (isWideScreen) Expanded(child: Container()),
            if (isWideScreen)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  OutlinedButton(
                      onPressed: () {
                        _handleLogout(context);
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(10),
                        child: Row(
                          children: [
                            Icon(
                              Icons.logout,
                              color: theme.cautionColor,
                              size: 15,
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            Text(
                              TIM_t("退出登录"),
                              style: TextStyle(
                                  color: theme.cautionColor, fontSize: 16),
                            )
                          ],
                        ),
                      ))
                ],
              ),
            if (isWideScreen)
              const SizedBox(
                height: 40,
              ),
            if (isWideScreen)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  OutlinedButton(
                      onPressed: () {
                        TUIKitWidePopup.showPopupWindow(
                            operationKey: TUIKitWideModalOperationKey.settings,
                            context: context,
                            theme: theme,
                            title: TIM_t("设置"),
                            width: MediaQuery.of(context).size.width * 0.7,
                            height: MediaQuery.of(context).size.height * 0.7,
                            child: (closeFunc) =>
                                Settings(closeFunc: closeFunc));
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(4),
                        child: Row(
                          children: [
                            Icon(
                              Icons.settings,
                              color: theme.darkTextColor,
                              size: 14,
                            ),
                            const SizedBox(
                              width: 4,
                            ),
                            Text(
                              TIM_t("设置"),
                              style: TextStyle(color: theme.darkTextColor),
                            )
                          ],
                        ),
                      )),
                  const SizedBox(
                    width: 40,
                  ),
                  OutlinedButton(
                      onPressed: () {
                        TUIKitWidePopup.showPopupWindow(
                            operationKey: TUIKitWideModalOperationKey.contactUs,
                            context: context,
                            theme: theme,
                            title: TIM_t("联系我们"),
                            width: MediaQuery.of(context).size.width * 0.6,
                            height: MediaQuery.of(context).size.height * 0.6,
                            child: (closeFunc) =>
                                ContactUs(closeFunc: closeFunc));
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(4),
                        child: Row(
                          children: [
                            Icon(
                              Icons.mail_outline,
                              color: theme.darkTextColor,
                              size: 14,
                            ),
                            const SizedBox(
                              width: 4,
                            ),
                            Text(
                              TIM_t("联系我们"),
                              style: TextStyle(color: theme.darkTextColor),
                            )
                          ],
                        ),
                      )),
                  const SizedBox(
                    width: 40,
                  ),
                  OutlinedButton(
                      onPressed: () {
                        TUIKitWidePopup.showPopupWindow(
                            operationKey: TUIKitWideModalOperationKey.aboutUs,
                            context: context,
                            theme: theme,
                            title: TIM_t("关于我们"),
                            width: MediaQuery.of(context).size.width * 0.6,
                            height: MediaQuery.of(context).size.height * 0.6,
                            child: (closeFunc) =>
                                AboutUs(closeFunc: closeFunc));
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(4),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: theme.darkTextColor,
                              size: 14,
                            ),
                            const SizedBox(
                              width: 4,
                            ),
                            Text(
                              TIM_t("关于"),
                              style: TextStyle(color: theme.darkTextColor),
                            )
                          ],
                        ),
                      )),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
